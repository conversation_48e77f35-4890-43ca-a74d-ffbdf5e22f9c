{
    "workbench.colorTheme": "CodeSandbox",
    "security.workspace.trust.untrustedFiles": "open",
    "explorer.confirmDelete": false,
    "diffEditor.ignoreTrimWhitespace": false,
    "terminal.integrated.enableMultiLinePasteWarning": "never",
    "explorer.confirmDragAndDrop": false,
    "workbench.startupEditor": "none",
    "git.autofetch": true,
    "git.enableSmartCommit": true,
    "diffEditor.maxComputationTime": 0,
    "background.autoInstall": true,
    "background.windowBackgrounds": [
        "C:/Users/<USER>/OneDrive/Pictures/photo_2024-07-06_22-19-42.jpg",
        "c:/Users/<USER>/Desktop/DEVELOPER/photo_2024-07-06_22-19-42.jpg",
        "c:/Users/<USER>/OneDrive/Desktop/DEVELOPER/photo_2024-07-06_22-19-42.jpg",
        "c:/Users/<USER>/Desktop/DEVELOPER/photo_2024-07-06_22-19-42.jpg"
    ],
    "workbench.editor.empty.hint": "hidden",
    "[python]": {
        "diffEditor.ignoreTrimWhitespace": false
    },
    
    "diffEditor.codeLens": true,
    "geminicodeassist.project": "",
    "github.copilot.selectedCompletionModel": "",
    "github.copilot.chat.languageContext.fix.typescript.enabled": true,
    "github.copilot.chat.languageContext.inline.typescript.enabled": true,
    "github.copilot.chat.languageContext.typescript.enabled": true,
    "github.copilot.nextEditSuggestions.enabled": true,
    "chat.mcp.discovery.enabled": true,
    "github.copilot.chat.agent.thinkingTool": true,
    "github.copilot.chat.startDebugging.enabled": true,
    "gitlens.ai.model": "vscode",
    "gitlens.ai.vscode.model": "copilot:gpt-4.1",
    "gitlens.ai.largePromptWarningThreshold": 128000,
    "github.copilot.chat.agent.runTasks": true,
    "github.copilot.chat.edits.suggestRelatedFilesForTests": true,
    "github.copilot.chat.scopeSelection": true,
    "github.copilot.chat.editor.temporalContext.enabled": true,
    "github.copilot.chat.generateTests.codeLens": true,
    "github.copilot.chat.edits.temporalContext.enabled": true,
    "chat.agent.maxRequests": 50,
     
    "githubPullRequests.experimental.chat": true,
    "githubPullRequests.experimental.useQuickChat": true,
    "github.copilot.chat.commitMessageGeneration.instructions": [
        {
            "text": "Use the Conventional Commits format: <type>(<scope>): <description>. Types include: feat, fix, docs, style, refactor, perf, test, chore. Examples: feat(api): add new endpoint, fix(auth): resolve login issue, docs(readme): update installation steps, write commit message in the format of <type>(<scope>): <description>. and deatiled",
        }
    ],
    "mcp": {
        "servers": {
            "github": {
                "command": "npx",
                "args": [
                    "-y",
                    "@modelcontextprotocol/server-github"
                ],
                "env": {
                    "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
                }
            },
            "Websearch": {
                "command": "npx",
                "args": [
                    "-y",
                    "@oevortex/ddg_search@latest"
                ]
            },
            "mcp-deepwiki": {
                "command": "npx",
                "args": [
                    "-y",
                    "mcp-deepwiki@latest"
                ]
            },
            "hf-mcp-server": {
                "url": "https://huggingface.co/mcp",
                "headers": {
                    "Authorization": "*************************************"
                }
            }
        }
    },
    "github.copilot.chat.edits.suggestRelatedFilesFromGitHistory": true,
    "git.confirmSync": false,
    "gitlens.advanced.messages": {
        "suppressLineUncommittedWarning": true
    },
    "github.copilot.chat.codesearch.enabled": true,
    "geminicodeassist.chat.enableStreaming": true,
    "geminicodeassist.inlineSuggestions.enableAuto": true,
    "grunt.autoDetect": "on",
    "powershell.powerShellDefaultVersion": "PowerShell (x64)",
    "chat.editing.autoAcceptDelay": 30,
    "editor.inlineSuggest.syntaxHighlightingEnabled": true,
    "zencoder.requirementsTool.enable": true,
    "github.copilot.chat.agent.currentEditorContext.enabled": true,
    "github.copilot.chat.byok.ollamaEndpoint": "http://localhost:11434",
    "zencoder.codeCompletion.enable": true,
    "zencoder.excludeGitIgnoreFromIndexing": true,
    "zencoder.mcpServers": {

        "github": {
            "command": "npx",
            "args": [
                "-y",
                "@modelcontextprotocol/server-github"
            ],
            "env": {
                "GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"
            }
        },
        // "desktop-commander": {
        //     "command": "npx",
        //     "args": [
        //         "-y",
        //         "@wonderwhy-er/desktop-commander"
        //     ]
        // },
        "ddg-search": {
            "command": "npx",
            "args": [
                "-y",
                "@oevortex/ddg_search"
            ]
        }
    },
    "zencoder.shellTool.allowedCommands": [
        "whoami",
        "find",
        "sort",
        "cd",
        "echo",
        "ls",
        "pwd",
        "cat",
        "head",
        "tail",
        "uname",
        "id",
        "env",
        "printenv",
        "df",
        "free",
        "ps",
        "grep",
        "uniq",
        "wc",
        "diff",
        "dir",
        "tree",
        "chdir",
        "type",
        "help",
        "ver",
        "systeminfo",
        "ipconfig",
        "tasklist",
        "hostname",
        "netstat",
        "which",
        "awk",
        "git status",
        "git diff",
        "git log",
        "git branch",
    ],
    "zencoder.enableRepoIndexing": true,
    "zencoder.enableShellTool": true,
    "files.autoSave": "afterDelay",
    "editor.bracketPairColorization.independentColorPoolPerBracketType": true,
    "github.copilot.chat.completionContext.typescript.mode": "on",
    "codium.enterprise.onPremService.enable": true,
    "codium.codeCompletion.enable": true,
    "zencoder.shellTool.commandConfirmationPolicy": "neverAsk",
    "zencoder.displayDebugInfo": true,
    "copilot.vision.provider": "Anthropic",
    "copilot.vision.model": "",
    "remote.WSL.debug": true,
    "mcpServers": {
        "Websearch": {
            "command": "npx",
            "env": {},
            "args": [
                "-y",
                "@oevortex/ddg_search@latest"
            ]
        },
        "desktop-commander": {
            "command": "npx",
            "env": {},
            "args": [
                "-y",
                "@wonderwhy-er/desktop-commander"
            ]
        }
    },
    "github.copilot.nextEditSuggestions.fixes": true,
    "githubIssues.alwaysPromptForNewIssueRepo": true,
    "editor.inlineSuggest.suppressSuggestions": true,
    "explorer.excludeGitIgnore": true,
    "editor.defaultFormatter": "GitHub.copilot",
    "geminicodeassist.customCommands": {
        "add-docstrings": "Generate professional and comprehensive docstrings/JSDoc comments for the selected code. For functions/methods, include a description of what it does, its parameters (name, type, description), and what it returns (type, description). For classes, provide a summary of the class's purpose and its main attributes/methods. Follow [Specify Language, e.g., Python PEP 257, JSDoc] conventions."
    },
    "augment.completions.enableAutomaticCompletions": false,
    "augment.conflictingCodingAssistantCheck": false,
    "augment.enableEmptyFileHint": false,
    "github.copilot.enable": {
        "*": true,
        "plaintext": false,
        "markdown": true,
        "scminput": false,
        "python": true
    },
    "geminicodeassist.contextExclusionFile": ".gitignore",
    "geminicodeassist.verboseLogging": true,
    "[markdown]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "workbench.editor.enablePreviewFromCodeNavigation": true,
    "workbench.editor.enablePreviewFromQuickOpen": true,
    "inlineChat.enableV2": true,
    "chat.edits2.enabled": true,
    "chat.renderRelatedFiles": true,
    "githubPullRequests.useReviewMode": "auto",
    "editor.suggest.preview": true,
    "markdown.preview.breaks": true,
    "editor.codeActions.triggerOnFocusChange": true,
    "inlineChat.lineEmptyHint": true,
    "gitlens.codeLens.includeSingleLineSymbols": true,
    "powershell.integratedConsole.useLegacyReadLine": true,
    "chat.tools.autoApprove": true,
    "inlineChat.finishOnType": true,
    "inlineChat.hideOnRequest": true,
    "python.analysis.aiCodeActions": {
    
        "generateSymbol": true,
        "convertLambdaToNamedFunction": true,
        "convertFormatString": true,
        "generateDocstring": false,
        "implementAbstractClasses": true
    },
    "editor.experimental.preferTreeSitter.regex": true,
    "editor.wordWrap": "on",
    "workbench.editor.enablePreview": false,
    "editor.experimental.asyncTokenization": false,
    "editor.emptySelectionClipboard": false,
    "makefile.configureOnOpen": false,
    "extensions.ignoreRecommendations": true,
    "mediaPreview.video.autoPlay": true,
    "mediaPreview.video.loop": true,
    "merge-conflict.autoNavigateNextConflict.enabled": true,
    "powershell.codeFormatting.autoCorrectAliases": true,
    "powershell.integratedConsole.suppressStartupBanner": true,
    "python.terminal.activateEnvInCurrentTerminal": true,
    "githubPullRequests.pullRequestDescription": "Copilot",
    "diffEditor.experimental.showMoves": true,
    "diffEditor.experimental.useTrueInlineView": true,
    "editor.minimap.autohide": true,
    "editor.inlineSuggest.edits.showCollapsed": true,
    "files.autoSaveWhenNoErrors": true,
    "chat.setup.continueLaterIndicator": true,
    "chat.setup.signInWithAlternateProvider": true,
    "github.codespaces.allowUnsafeConnections": true,
    "github.copilot.chat.notebook.followCellExecution.enabled": true,
    "accessibility.signals.chatUserActionRequired": {
        "sound": "auto"
    },
    "window.menuStyle": "custom",
    "workbench.settings.showAISearchToggle": true,
    "search.searchView.keywordSuggestions": true,
    "search.searchView.semanticSearchBehavior": "auto",
    "terminal.integrated.suggest.enabled": true,
    "chat.mcp.serverSampling": {
        "Global in Code: github": {
            "allowedModels": [
                "github.copilot-chat/gpt-4.1",
                "github.copilot-chat/claude-3.5-sonnet",
                "github.copilot-chat/claude-3.7-sonnet",
                "github.copilot-chat/claude-3.7-sonnet-thought",
                "github.copilot-chat/claude-sonnet-4",
                "github.copilot-chat/gemini-2.0-flash-001",
                "github.copilot-chat/gemini-2.5-pro",
                "github.copilot-chat/gpt-4o",
                "github.copilot-chat/o1",
                "github.copilot-chat/o3-mini",
                "github.copilot-chat/o4-mini"
            ]
        }
    },
    "python.defaultInterpreterPath": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python.exe",
    "notebook.defaultFormatter": "GitHub.copilot",
    "search.experimental.closedNotebookRichContentResults": true,
    "css.format.spaceAroundSelectorSeparator": true,
    "html.format.indentHandlebars": true,
    "remote.SSH.useCurlAndWgetConfigurationFiles": true,
    "gitlens.menus": {
    
        "editor": {
            "blame": true,
            "clipboard": true,
            "compare": true,
            "history": true,
            "remote": true
        },
        "editorGroup": {
            "blame": true,
            "compare": true
        },
        "editorGutter": {
            "compare": true,
            "remote": true,
            "share": true
        },
        "editorTab": {
            "clipboard": true,
            "compare": true,
            "history": true,
            "remote": true
        },
        "explorer": {
            "clipboard": true,
            "compare": true,
            "history": true,
            "remote": true
        },
        "ghpr": {
            "worktree": true
        },
        "scm": {
            "graph": true
        },
        "scmRepositoryInline": {
            "generateCommitMessage": true,
            "graph": true,
            "stash": true
        },
        "scmRepository": {
            "authors": true,
            "generateCommitMessage": true,
            "patch": true,
            "graph": false
        },
        "scmGroupInline": {
            "stash": true
        },
        "scmGroup": {
            "compare": true,
            "openClose": true,
            "patch": true,
            "stash": true
        },
        "scmItemInline": {
            "stash": false
        },
        "scmItem": {
            "clipboard": true,
            "compare": true,
            "history": true,
            "patch": true,
            "remote": true,
            "share": true,
            "stash": true
        }
    },
    "git.useCommitInputAsStashMessage": true,
    "git.alwaysSignOff": true,
    "terminal.integrated.suggest.providers": {
        "terminal-suggest": true,           // General terminal completions
        "pwsh-shell-integration": true,     // PowerShell shell integration
        "lsp": true,                        // Language Server Protocol suggestions
        "shell-integration": true           // General shell integration
    },
    "terminal.integrated.suggest.quickSuggestions": {
        "commands": "on",
        "arguments": "on",
        "unknown": "on"
    },
    "terminal.integrated.shellIntegration.environmentReporting": true,
    "terminal.integrated.suggest.windowsExecutableExtensions": {
        "bat": true,
        "cmd": true,
        "com": true,
        "exe": true,
        "jar": true,
        "js": true,
        "msi": true,
        "pl": true,
        "ps1": true,
        "py": true,
        "rb": true,
        "sh": true,
        "vbs": true
    },
    "[jsonc]": {
        "editor.defaultFormatter": "vscode.json-language-features"
    },
    "[powershell]": {
        "editor.defaultFormatter": "ms-vscode.powershell"
    },
    "python.analysis.addHoverSummaries": true,
    "files.autoSaveDelay": 100,
    "remote.SSH.remotePlatform": {
        "gcp-instance": "linux",
        "ssh-practical-blackwell7-kfj79.view-3.tempo-dev.app": "linux"
    },
    "python.createEnvironment.trigger": "off",
    "workbench.editorAssociations": {
        "*.copilotmd": "vscode.markdown.preview.editor",
        "*.db": "sqlite-viewer.view"
    },
    "editor.unicodeHighlight.invisibleCharacters": false,
    "explorer.fileNesting.patterns": {
        "*.ts": "${capture}.js",
        "*.js": "${capture}.js.map, ${capture}.min.js, ${capture}.d.ts",
        "*.jsx": "${capture}.js",
        "*.tsx": "${capture}.ts",
        "tsconfig.json": "tsconfig.*.json",
        "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, bun.lockb, bun.lock",
        "*.sqlite": "${capture}.${extname}-*",
        "*.db": "${capture}.${extname}-*",
        "*.sqlite3": "${capture}.${extname}-*",
        "*.db3": "${capture}.${extname}-*",
        "*.sdb": "${capture}.${extname}-*",
        "*.s3db": "${capture}.${extname}-*"
    },
    "C_Cpp.copilotHover": "enabled",
    "geminicodeassist.codeGenerationPaneViewEnabled": true,
    "geminicodeassist.agentDebugMode": true,
    "geminicodeassist.agentYoloMode": true,
    "githubPullRequests.codingAgent.enabled": true,
    "geminicodeassist.updateChannel": "Insiders",
    "gitlens.views.workspaces.includeWorkingTree": true,
    "gitlens.views.workspaces.showStashes": true,
    "workbench.experimental.cloudChanges.partialMatches.enabled": true,
    "workbench.experimental.share.enabled": true,
    "terminal.integrated.stickyScroll.enabled": true,
    "augment.completions.enableQuickSuggestions": false,
    "augment.nextEdit.enableBackgroundSuggestions": false,
    "augment.nextEdit.enableAutoApply": false,
    "python.analysis.userFileIndexingLimit": -1,
}
